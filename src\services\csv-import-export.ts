/**
 * CSV Import/Export Service for SmartBoutique
 * Handles admin import/export functionality with validation and error handling
 */

import {
  CSVUtils,
  PRODUCT_COLUMNS,
  USER_COLUMNS,
  SALES_COLUMNS,
  DEBT_COLUMNS,
  EXPENSE_COLUMNS,
  SETTINGS_COLUMNS,
  settingsToCSVArray,
  csvArrayToSettings
} from './csv-utils';
import { csvStorageService } from './csv-storage';
import { adaptiveStorageService } from './adaptive-storage';
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';

export interface ImportResult {
  success: boolean;
  message: string;
  errors: string[];
  importedCount: number;
}

export interface ExportResult {
  success: boolean;
  message: string;
  filePath?: string;
  data?: string;
}

export class CSVImportExportService {
  
  /**
   * Export all data as a complete backup package
   */
  async exportAllData(): Promise<ExportResult> {
    try {
      const [products, users, sales, debts, expenses, settings] = await Promise.all([
        adaptiveStorageService.getProducts(),
        adaptiveStorageService.getUsers(),
        adaptiveStorageService.getSales(),
        adaptiveStorageService.getDebts(),
        adaptiveStorageService.getExpenses(),
        adaptiveStorageService.getSettings()
      ]);

      const exportData = {
        exportDate: new Date().toISOString(),
        products: CSVUtils.arrayToCSV(products, PRODUCT_COLUMNS),
        users: CSVUtils.arrayToCSV(users, USER_COLUMNS),
        sales: CSVUtils.arrayToCSV(sales, SALES_COLUMNS),
        debts: CSVUtils.arrayToCSV(debts, DEBT_COLUMNS),
        expenses: CSVUtils.arrayToCSV(expenses, EXPENSE_COLUMNS),
        settings: CSVUtils.arrayToCSV(settingsToCSVArray(settings), SETTINGS_COLUMNS)
      };

      // Create a comprehensive backup file with UTF-8 BOM for Excel compatibility
      const backupContent = `SmartBoutique - Sauvegarde Complète
Date d'exportation: ${exportData.exportDate}

=== PRODUITS ===
${exportData.products}

=== UTILISATEURS ===
${exportData.users}

=== VENTES ===
${exportData.sales}

=== DETTES ===
${exportData.debts}

=== DÉPENSES ===
${exportData.expenses}

=== PARAMÈTRES ===
${exportData.settings}
`;

      // Add UTF-8 BOM for Excel compatibility with French characters
      const backupContentWithBOM = '\uFEFF' + backupContent;

      return {
        success: true,
        message: 'Exportation complète réussie (compatible Excel)',
        data: backupContentWithBOM
      };
    } catch (error) {
      console.error('Erreur lors de l\'exportation complète:', error);
      return {
        success: false,
        message: 'Erreur lors de l\'exportation: ' + (error as Error).message
      };
    }
  }

  /**
   * Export specific data type
   */
  async exportData(dataType: 'products' | 'users' | 'sales' | 'debts' | 'expenses'): Promise<ExportResult> {
    try {
      let data: any[] = [];
      let columns: any[] = [];
      let fileName = '';

      switch (dataType) {
        case 'products':
          data = await adaptiveStorageService.getProducts();
          columns = PRODUCT_COLUMNS;
          fileName = 'produits';
          break;
        case 'users':
          data = await adaptiveStorageService.getUsers();
          columns = USER_COLUMNS;
          fileName = 'utilisateurs';
          break;
        case 'sales':
          data = await adaptiveStorageService.getSales();
          columns = SALES_COLUMNS;
          fileName = 'ventes';
          break;
        case 'debts':
          data = await adaptiveStorageService.getDebts();
          columns = DEBT_COLUMNS;
          fileName = 'dettes';
          break;
        case 'expenses':
          data = await adaptiveStorageService.getExpenses();
          columns = EXPENSE_COLUMNS;
          fileName = 'depenses';
          break;
      }

      const csvContent = CSVUtils.arrayToCSV(data, columns);
      
      return {
        success: true,
        message: `Exportation ${fileName} réussie (${data.length} enregistrements)`,
        data: csvContent
      };
    } catch (error) {
      console.error(`Erreur lors de l'exportation ${dataType}:`, error);
      return {
        success: false,
        message: 'Erreur lors de l\'exportation: ' + (error as Error).message
      };
    }
  }

  /**
   * Import products from CSV
   */
  async importProducts(csvContent: string, replaceExisting: boolean = false): Promise<ImportResult> {
    try {
      const importedProducts = CSVUtils.csvToArray(csvContent, PRODUCT_COLUMNS);
      
      // Validate data
      const validation = CSVUtils.validateCSVData(importedProducts, PRODUCT_COLUMNS);
      if (!validation.isValid) {
        return {
          success: false,
          message: 'Données invalides détectées',
          errors: validation.errors,
          importedCount: 0
        };
      }

      // Process import
      let finalProducts = importedProducts;
      if (!replaceExisting) {
        const existingProducts = await adaptiveStorageService.getProducts();
        // Merge with existing, avoiding duplicates by ID
        const existingIds = new Set(existingProducts.map(p => p.id));
        const newProducts = importedProducts.filter(p => !existingIds.has(p.id));
        finalProducts = [...existingProducts, ...newProducts];
      }

      await adaptiveStorageService.setProducts(finalProducts);

      return {
        success: true,
        message: `${importedProducts.length} produits importés avec succès`,
        errors: [],
        importedCount: importedProducts.length
      };
    } catch (error) {
      console.error('Erreur lors de l\'importation des produits:', error);
      return {
        success: false,
        message: 'Erreur lors de l\'importation: ' + (error as Error).message,
        errors: [(error as Error).message],
        importedCount: 0
      };
    }
  }

  /**
   * Import users from CSV
   */
  async importUsers(csvContent: string, replaceExisting: boolean = false): Promise<ImportResult> {
    try {
      const importedUsers = CSVUtils.csvToArray(csvContent, USER_COLUMNS);
      
      const validation = CSVUtils.validateCSVData(importedUsers, USER_COLUMNS);
      if (!validation.isValid) {
        return {
          success: false,
          message: 'Données invalides détectées',
          errors: validation.errors,
          importedCount: 0
        };
      }

      let finalUsers = importedUsers;
      if (!replaceExisting) {
        const existingUsers = await adaptiveStorageService.getUsers();
        const existingIds = new Set(existingUsers.map(u => u.id));
        const newUsers = importedUsers.filter(u => !existingIds.has(u.id));
        finalUsers = [...existingUsers, ...newUsers];
      }

      await adaptiveStorageService.setUsers(finalUsers);

      return {
        success: true,
        message: `${importedUsers.length} utilisateurs importés avec succès`,
        errors: [],
        importedCount: importedUsers.length
      };
    } catch (error) {
      console.error('Erreur lors de l\'importation des utilisateurs:', error);
      return {
        success: false,
        message: 'Erreur lors de l\'importation: ' + (error as Error).message,
        errors: [(error as Error).message],
        importedCount: 0
      };
    }
  }

  /**
   * Generate CSV templates for import
   */
  generateTemplate(dataType: 'products' | 'users' | 'sales' | 'debts' | 'expenses'): string {
    switch (dataType) {
      case 'products':
        return CSVUtils.generateTemplate(PRODUCT_COLUMNS);
      case 'users':
        return CSVUtils.generateTemplate(USER_COLUMNS);
      case 'sales':
        return CSVUtils.generateTemplate(SALES_COLUMNS);
      case 'debts':
        return CSVUtils.generateTemplate(DEBT_COLUMNS);
      case 'expenses':
        return CSVUtils.generateTemplate(EXPENSE_COLUMNS);
      default:
        return '';
    }
  }

  /**
   * Create automatic backup
   */
  async createAutomaticBackup(): Promise<ExportResult> {
    try {
      const backupResult = await this.exportAllData();
      if (backupResult.success && backupResult.data) {
        // Store backup with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupKey = `backup_${timestamp}`;
        
        // In a real implementation, you might want to store this in a specific backup location
        // For now, we'll just return the backup data
        
        return {
          success: true,
          message: `Sauvegarde automatique créée: ${timestamp}`,
          data: backupResult.data
        };
      }
      
      return backupResult;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde automatique:', error);
      return {
        success: false,
        message: 'Erreur lors de la sauvegarde automatique: ' + (error as Error).message
      };
    }
  }

  /**
   * Get sample CSV data for demonstration
   */
  getSampleCSVData(dataType: 'products' | 'users' | 'sales' | 'debts' | 'expenses'): string {
    // This would return sample data for demonstration purposes
    switch (dataType) {
      case 'products':
        return `ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification
SAMPLE1,Produit Exemple,Description du produit exemple,5600,2,SAMPLE123,Alimentation,50,10,1234567890,2024-01-01,2024-01-01`;
      case 'users':
        return `ID,Nom,Email,Rôle,Mot de Passe,Date de Création,Actif
SAMPLE1,Utilisateur Exemple,<EMAIL>,employee,motdepasse123,2024-01-01,Oui`;
      default:
        return this.generateTemplate(dataType);
    }
  }
}

export const csvImportExportService = new CSVImportExportService();
